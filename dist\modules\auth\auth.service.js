"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
let AuthService = class AuthService {
    constructor(firebaseService, usersService) {
        this.firebaseService = firebaseService;
        this.usersService = usersService;
    }
    async register(registerDto) {
        try {
            const userRecord = await this.firebaseService.getAuth().createUser({
                email: registerDto.email,
                password: registerDto.password,
                displayName: registerDto.username,
            });
            const userData = {
                userId: userRecord.uid,
                username: registerDto.username,
                email: registerDto.email,
                role: registerDto.role || roles_decorator_1.UserRole.USER,
                language: registerDto.language || "en",
                createdAt: new Date(),
                lastLogin: null,
            };
            await this.usersService.create(userData);
            return {
                message: "User registered successfully",
                userId: userRecord.uid,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException(`Registration failed: ${error.message}`);
        }
    }
    async validateUser(uid) {
        try {
            const userRecord = await this.firebaseService.getAuth().getUser(uid);
            const userData = await this.usersService.findById(uid);
            return {
                ...userRecord,
                ...userData,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException("User validation failed");
        }
    }
    async updateLastLogin(userId) {
        await this.usersService.updateLastLogin(userId);
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Function, Function])
], AuthService);
//# sourceMappingURL=auth.service.js.map