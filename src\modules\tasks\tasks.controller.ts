import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth, Api<PERSON><PERSON>y } from "@nestjs/swagger"
import { TasksService } from "./tasks.service"
import { CreateTaskDto } from "./dto/create-task.dto"
import { UpdateTaskDto } from "./dto/update-task.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"

@ApiTags("Tasks")
@Controller("tasks")
export class TasksController {
  constructor(private readonly tasksService: TasksService) {}

  @Post()
  @ApiOperation({ summary: "Create a new task" })
  create(@Body() createTaskDto: CreateTaskDto) {
    return this.tasksService.create(createTaskDto)
  }

  @Get()
  @ApiOperation({ summary: "Get all tasks" })
  @ApiQuery({ name: "userId", required: false })
  @ApiQuery({ name: "robotId", required: false })
  findAll(@Query('userId') userId?: string, @Query('robotId') robotId?: string) {
    if (userId) {
      return this.tasksService.findByUser(userId)
    }
    if (robotId) {
      return this.tasksService.findByRobot(robotId)
    }
    return this.tasksService.findAll()
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get task by ID' })
  findOne(@Param('id') id: string) {
    return this.tasksService.findById(id);
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update task" })
  update(@Param('id') id: string, @Body() updateTaskDto: UpdateTaskDto) {
    return this.tasksService.update(id, updateTaskDto)
  }

  @Patch(":id/status")
  @ApiOperation({ summary: "Update task status" })
  updateStatus(@Param('id') id: string, @Body('status') status: string) {
    return this.tasksService.updateStatus(id, status)
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete task' })
  remove(@Param('id') id: string) {
    return this.tasksService.remove(id);
  }
}
