import { Controller, Post, UseGuards, Get, Request } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger"
import type { AuthService } from "./auth.service"
import type { RegisterDto } from "./dto/register.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"

@ApiTags("Authentication")
@Controller("auth")
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post("register")
  @ApiOperation({ summary: "Register a new user" })
  async register(registerDto: RegisterDto) {
    return this.authService.register(registerDto)
  }

  @Get('profile')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  async getProfile(@Request() req) {
    const user = await this.authService.validateUser(req.user.uid);
    await this.authService.updateLastLogin(req.user.uid);
    return user;
  }
}
