import { Injectable, NotFoundException } from "@nestjs/common"
import type { FirebaseService } from "../../config/firebase/firebase.service"
import type { CreateBuildingDto } from "./dto/create-building.dto"
import type { UpdateBuildingDto } from "./dto/update-building.dto"

@Injectable()
export class BuildingsService {
  private readonly collection = "buildings"

  constructor(private firebaseService: FirebaseService) {}

  async create(createBuildingDto: CreateBuildingDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc()

    const buildingData = {
      ...createBuildingDto,
      buildingId: docRef.id,
      createdAt: new Date(),
    }

    await docRef.set(buildingData)
    return buildingData
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`Building with ID ${id} not found`)
    }

    return { id: doc.id, ...doc.data() }
  }

  async update(id: string, updateBuildingDto: UpdateBuildingDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    await docRef.update({
      ...updateBuildingDto,
      updatedAt: new Date(),
    })

    return this.findById(id)
  }

  async remove(id: string) {
    const firestore = this.firebaseService.getFirestore()
    await firestore.collection(this.collection).doc(id).delete()
    return { message: "Building deleted successfully" }
  }
}
