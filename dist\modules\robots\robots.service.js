"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RobotsService = void 0;
const common_1 = require("@nestjs/common");
let RobotsService = class RobotsService {
    constructor(firebaseService) {
        this.firebaseService = firebaseService;
        this.collection = "robots";
    }
    async create(createRobotDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc();
        const robotData = {
            ...createRobotDto,
            robotId: docRef.id,
            isConnected: false,
            createdAt: new Date(),
        };
        await docRef.set(robotData);
        return robotData;
    }
    async findAll() {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findById(id) {
        const firestore = this.firebaseService.getFirestore();
        const doc = await firestore.collection(this.collection).doc(id).get();
        if (!doc.exists) {
            throw new common_1.NotFoundException(`Robot with ID ${id} not found`);
        }
        return { id: doc.id, ...doc.data() };
    }
    async update(id, updateRobotDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(id);
        await docRef.update({
            ...updateRobotDto,
            updatedAt: new Date(),
        });
        return this.findById(id);
    }
    async updateStatus(id, status) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(id);
        await docRef.update({
            ...status,
            lastStatusUpdate: new Date(),
        });
        return this.findById(id);
    }
    async remove(id) {
        const firestore = this.firebaseService.getFirestore();
        await firestore.collection(this.collection).doc(id).delete();
        return { message: "Robot deleted successfully" };
    }
};
exports.RobotsService = RobotsService;
exports.RobotsService = RobotsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Function])
], RobotsService);
//# sourceMappingURL=robots.service.js.map