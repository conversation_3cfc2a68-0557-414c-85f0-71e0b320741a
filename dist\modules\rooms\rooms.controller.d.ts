import { RoomsService } from "./rooms.service";
import { CreateRoomDto } from "./dto/create-room.dto";
import { UpdateRoomDto } from "./dto/update-room.dto";
export declare class RoomsController {
    private readonly roomsService;
    constructor(roomsService: RoomsService);
    create(createRoomDto: CreateRoomDto): Promise<{
        roomId: string;
        createdAt: Date;
        roomNumber: string;
        roomName: string;
        floorId: string;
        roomType: string;
        area: number;
        capacity: number;
        requiresDisinfection?: boolean;
        lastDisinfected?: string;
    }>;
    findAll(floorId?: string): Promise<{
        id: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateRoomDto: UpdateRoomDto): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
