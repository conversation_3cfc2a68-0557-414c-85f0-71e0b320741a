import { <PERSON>, Get, Post, Param, Delete, UseGuards, Query } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQ<PERSON>y } from "@nestjs/swagger"
import type { HistoryService } from "./history.service"
import type { CreateHistoryDto } from "./dto/create-history.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("History")
@Controller("history")
@UseGuards(FirebaseAuthGuard, RolesGuard)
@ApiBearerAuth()
export class HistoryController {
  constructor(private readonly historyService: HistoryService) {}

  @Post()
  @ApiOperation({ summary: "Log a new history record" })
  create(createHistoryDto: CreateHistoryDto) {
    return this.historyService.create(createHistoryDto)
  }

  @Get()
  @ApiOperation({ summary: "Get all history records" })
  @ApiQuery({ name: "taskId", required: false })
  @ApiQuery({ name: "userId", required: false })
  @ApiQuery({ name: "robotId", required: false })
  findAll(@Query('taskId') taskId?: string, @Query('userId') userId?: string, @Query('robotId') robotId?: string) {
    if (taskId) {
      return this.historyService.findByTask(taskId)
    }
    if (userId) {
      return this.historyService.findByUser(userId)
    }
    if (robotId) {
      return this.historyService.findByRobot(robotId)
    }
    return this.historyService.findAll()
  }

  @Get("reports")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Generate history reports (Admin only)" })
  @ApiQuery({ name: "startDate", required: false })
  @ApiQuery({ name: "endDate", required: false })
  @ApiQuery({ name: "userId", required: false })
  @ApiQuery({ name: "robotId", required: false })
  @ApiQuery({ name: "action", required: false })
  generateReport(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('userId') userId?: string,
    @Query('robotId') robotId?: string,
    @Query('action') action?: string,
  ) {
    const filters = { startDate, endDate, userId, robotId, action }
    return this.historyService.generateReport(filters)
  }

  @Get("export")
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: "Export history data (Admin only)" })
  @ApiQuery({ name: "startDate", required: false })
  @ApiQuery({ name: "endDate", required: false })
  @ApiQuery({ name: "userId", required: false })
  @ApiQuery({ name: "robotId", required: false })
  @ApiQuery({ name: "action", required: false })
  exportData(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('userId') userId?: string,
    @Query('robotId') robotId?: string,
    @Query('action') action?: string,
  ) {
    const filters = { startDate, endDate, userId, robotId, action }
    return this.historyService.exportData(filters)
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get history record by ID' })
  findOne(@Param('id') id: string) {
    return this.historyService.findById(id);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete history record (Admin only)' })
  remove(@Param('id') id: string) {
    return this.historyService.remove(id);
  }
}
