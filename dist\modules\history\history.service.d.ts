import { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateHistoryDto } from "./dto/create-history.dto";
export interface HistoryRecord {
    id: string;
    taskId?: string;
    userId?: string;
    robotId?: string;
    zoneId?: string;
    action?: string;
    timestamp?: string | Date;
    duration?: string;
    result?: string;
    errorMessage?: string;
    batteryUsed?: string;
    disinfectionEfficiency?: string;
}
export declare class HistoryService {
    private firebaseService;
    private readonly collection;
    constructor(firebaseService: FirebaseService);
    create(createHistoryDto: CreateHistoryDto): Promise<{
        historyId: string;
        timestamp: Date;
        taskId: string;
        userId: string;
        robotId: string;
        zoneId?: string;
        action: string;
        duration?: number;
        result?: string;
        errorMessage?: string;
        batteryUsed?: number;
        disinfectionEfficiency?: number;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findByTask(taskId: string): Promise<{
        id: string;
    }[]>;
    findByUser(userId: string): Promise<{
        id: string;
    }[]>;
    findByRobot(robotId: string): Promise<{
        id: string;
    }[]>;
    findById(id: string): Promise<{
        id: string;
    }>;
    generateReport(filters: any): Promise<{
        id: string;
    }[]>;
    exportData(filters: any): Promise<{
        data: HistoryRecord[];
        filename: string;
        contentType: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
