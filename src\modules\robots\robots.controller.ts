import { <PERSON>, Get, Post, Patch, Param, Delete, UseGuards, Body } from "@nestjs/common"
import { ApiTags, ApiOperation, ApiBearerAuth } from "@nestjs/swagger"
import { RobotsService } from "./robots.service"
import { CreateRobotDto } from "./dto/create-robot.dto"
import { UpdateRobotDto } from "./dto/update-robot.dto"
import { FirebaseAuthGuard } from "../../common/guards/firebase-auth.guard"
import { RolesGuard } from "../../common/guards/roles.guard"
import { Roles, UserRole } from "../../common/decorators/roles.decorator"

@ApiTags("Robots")
@Controller("robots")
export class RobotsController {
  constructor(private readonly robotsService: RobotsService) {}

  @Post()
  @ApiOperation({ summary: "Register a new robot" })
  create(@Body() createRobotDto: CreateRobotDto) {
    return this.robotsService.create(createRobotDto)
  }

  @Get()
  @ApiOperation({ summary: "Get all robots" })
  findAll() {
    return this.robotsService.findAll()
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get robot by ID' })
  findOne(@Param('id') id: string) {
    return this.robotsService.findById(id);
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update robot" })
  update(@Param('id') id: string, @Body() updateRobotDto: UpdateRobotDto) {
    return this.robotsService.update(id, updateRobotDto)
  }

  @Patch(":id/status")
  @ApiOperation({ summary: "Update robot status" })
  updateStatus(@Param('id') id: string, @Body() status: any) {
    return this.robotsService.updateStatus(id, status)
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete robot' })
  remove(@Param('id') id: string) {
    return this.robotsService.remove(id);
  }
}
