import { Injectable, UnauthorizedException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import { UsersService } from "../users/users.service"
import type { RegisterDto } from "./dto/register.dto"
import type { LoginDto } from "./dto/login.dto"
import { UserRole } from "@/common/decorators/roles.decorator"

@Injectable()
export class AuthService {
  constructor(
    private firebaseService: FirebaseService,
    private usersService: UsersService,
  ) {}

  async register(registerDto: RegisterDto) {
    try {
      console.log('Starting user registration for:', registerDto.email)

      // Create user in Firebase Auth
      const userRecord = await this.firebaseService.getAuth().createUser({
        email: registerDto.email,
        password: registerDto.password,
        displayName: registerDto.username,
      })

      console.log('Firebase user created successfully:', userRecord.uid)

      // Create user document in Firestore
      const userData = {
        userId: userRecord.uid,
        username: registerDto.username,
        email: registerDto.email,
        role: registerDto.role || UserRole.USER,
        language: registerDto.language || "en",
        createdAt: new Date().toISOString(),
        lastLogin: null,
      }

      await this.usersService.create(userData)
      console.log('User document created in Firestore')

      return {
        message: "User registered successfully",
        userId: userRecord.uid,
      }
    } catch (error) {
      console.error('Registration error:', error)

      // Provide more specific error messages
      if (error.code === 'auth/email-already-exists') {
        throw new UnauthorizedException('Email already exists')
      } else if (error.code === 'auth/invalid-email') {
        throw new UnauthorizedException('Invalid email address')
      } else if (error.code === 'auth/weak-password') {
        throw new UnauthorizedException('Password is too weak')
      } else if (error.message?.includes('certificate')) {
        throw new UnauthorizedException('Network connection issue. Please check your internet connection or contact your network administrator.')
      }

      throw new UnauthorizedException(`Registration failed: ${error.message}`)
    }
  }

  async validateUser(uid: string) {
    try {
      const userRecord = await this.firebaseService.getAuth().getUser(uid)
      const userData = await this.usersService.findById(uid)

      return {
        ...userRecord,
        ...userData,
      }
    } catch (error) {
      throw new UnauthorizedException("User validation failed")
    }
  }

  async login(loginDto: LoginDto) {
    try {
      console.log('Starting user login for:', loginDto.email)

      // For demonstration purposes, we'll create a custom token
      // In a real app, you'd verify the password against Firebase Auth

      // First, try to get the user by email from Firestore
      const userData = await this.usersService.findByEmail(loginDto.email)

      if (!userData) {
        throw new UnauthorizedException('User not found')
      }

      // Create a custom token for the user
      const customToken = await this.firebaseService.getAuth().createCustomToken(userData.id)

      console.log('Login successful for user:', userData.id)

      return {
        message: "Login successful",
        token: customToken,
        user: {
          userId: userData.id,
          email: (userData as any).email,
          username: (userData as any).username,
          role: (userData as any).role
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      throw new UnauthorizedException(`Login failed: ${error.message}`)
    }
  }

  async updateLastLogin(userId: string) {
    await this.usersService.updateLastLogin(userId)
  }
}
