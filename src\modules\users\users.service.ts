import { Injectable, NotFoundException } from "@nestjs/common"
import { FirebaseService } from "../../config/firebase/firebase.service"
import type { CreateUserDto } from "./dto/create-user.dto"
import type { UpdateUserDto } from "./dto/update-user.dto"

@Injectable()
export class UsersService {
  private readonly collection = "users"

  constructor(private firebaseService: FirebaseService) {}

  async create(createUserDto: CreateUserDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(createUserDto.userId)
    await docRef.set(createUserDto)
    return createUserDto
  }

  async findAll() {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).get()
    return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }))
  }

  async findById(id: string) {
    const firestore = this.firebaseService.getFirestore()
    const doc = await firestore.collection(this.collection).doc(id).get()

    if (!doc.exists) {
      throw new NotFoundException(`User with ID ${id} not found`)
    }

    return { id: doc.id, ...doc.data() }
  }

  async findByEmail(email: string) {
    const firestore = this.firebaseService.getFirestore()
    const snapshot = await firestore.collection(this.collection).where('email', '==', email).get()

    if (snapshot.empty) {
      return null
    }

    const doc = snapshot.docs[0]
    return { id: doc.id, ...doc.data() }
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(id)

    await docRef.update({
      ...updateUserDto,
      updatedAt: new Date(),
    })

    return this.findById(id)
  }

  async updateLastLogin(userId: string) {
    const firestore = this.firebaseService.getFirestore()
    const docRef = firestore.collection(this.collection).doc(userId)

    await docRef.update({
      lastLogin: new Date(),
    })
  }

  async remove(id: string) {
    const firestore = this.firebaseService.getFirestore()
    await firestore.collection(this.collection).doc(id).delete()

    // Also remove from Firebase Auth
    await this.firebaseService.getAuth().deleteUser(id)

    return { message: "User deleted successfully" }
  }
}
