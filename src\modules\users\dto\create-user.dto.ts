import { Is<PERSON>tring, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsDate } from "class-validator"
import { ApiProperty } from "@nestjs/swagger"
import { UserRole } from "../../../common/decorators/roles.decorator"

export class CreateUserDto {
  @ApiProperty({
    description: 'Unique user ID',
    example: 'user123'
  })
  @IsString()
  userId: string

  @ApiProperty({
    description: 'Username',
    example: 'walid'
  })
  @IsString()
  username: string

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>'
  })
  @IsEmail()
  email: string

  @ApiProperty({
    enum: UserRole,
    description: 'User role',
    example: UserRole.USER
  })
  @IsEnum(UserRole)
  role: UserRole

  @ApiProperty({
    required: false,
    description: 'Preferred language',
    example: 'en'
  })
  @IsOptional()
  @IsString()
  language?: string

  @ApiProperty({
    description: 'Creation date',
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsDate()
  createdAt: Date

  @ApiProperty({
    required: false,
    description: 'Last login date',
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDate()
  lastLogin?: Date
}
