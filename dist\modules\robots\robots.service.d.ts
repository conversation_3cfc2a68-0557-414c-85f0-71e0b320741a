import type { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateRobotDto } from "./dto/create-robot.dto";
import type { UpdateRobotDto } from "./dto/update-robot.dto";
export declare class RobotsService {
    private firebaseService;
    private readonly collection;
    constructor(firebaseService: FirebaseService);
    create(createRobotDto: CreateRobotDto): Promise<{
        robotId: string;
        isConnected: boolean;
        createdAt: Date;
        robotName: string;
        serialNumber: string;
        batteryLevel?: number;
        currentPosition?: string;
        ipAddress?: string;
        firmwareVersion?: string;
        maintenanceDate?: string;
        currentTaskId?: string;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findById(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateRobotDto: UpdateRobotDto): Promise<{
        id: string;
    }>;
    updateStatus(id: string, status: any): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
