"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoryController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const history_service_1 = require("./history.service");
const create_history_dto_1 = require("./dto/create-history.dto");
const firebase_auth_guard_1 = require("../../common/guards/firebase-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
let HistoryController = class HistoryController {
    constructor(historyService) {
        this.historyService = historyService;
    }
    create(createHistoryDto) {
        return this.historyService.create(createHistoryDto);
    }
    findAll(taskId, userId, robotId) {
        if (taskId) {
            return this.historyService.findByTask(taskId);
        }
        if (userId) {
            return this.historyService.findByUser(userId);
        }
        if (robotId) {
            return this.historyService.findByRobot(robotId);
        }
        return this.historyService.findAll();
    }
    generateReport(startDate, endDate, userId, robotId, action) {
        const filters = { startDate, endDate, userId, robotId, action };
        return this.historyService.generateReport(filters);
    }
    exportData(startDate, endDate, userId, robotId, action) {
        const filters = { startDate, endDate, userId, robotId, action };
        return this.historyService.exportData(filters);
    }
    findOne(id) {
        return this.historyService.findById(id);
    }
    remove(id) {
        return this.historyService.remove(id);
    }
};
exports.HistoryController = HistoryController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: "Log a new history record" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_history_dto_1.CreateHistoryDto]),
    __metadata("design:returntype", void 0)
], HistoryController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: "Get all history records" }),
    (0, swagger_1.ApiQuery)({ name: "taskId", required: false }),
    (0, swagger_1.ApiQuery)({ name: "userId", required: false }),
    (0, swagger_1.ApiQuery)({ name: "robotId", required: false }),
    __param(0, (0, common_1.Query)('taskId')),
    __param(1, (0, common_1.Query)('userId')),
    __param(2, (0, common_1.Query)('robotId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], HistoryController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)("reports"),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: "Generate history reports (Admin only)" }),
    (0, swagger_1.ApiQuery)({ name: "startDate", required: false }),
    (0, swagger_1.ApiQuery)({ name: "endDate", required: false }),
    (0, swagger_1.ApiQuery)({ name: "userId", required: false }),
    (0, swagger_1.ApiQuery)({ name: "robotId", required: false }),
    (0, swagger_1.ApiQuery)({ name: "action", required: false }),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __param(2, (0, common_1.Query)('userId')),
    __param(3, (0, common_1.Query)('robotId')),
    __param(4, (0, common_1.Query)('action')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String]),
    __metadata("design:returntype", void 0)
], HistoryController.prototype, "generateReport", null);
__decorate([
    (0, common_1.Get)("export"),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: "Export history data (Admin only)" }),
    (0, swagger_1.ApiQuery)({ name: "startDate", required: false }),
    (0, swagger_1.ApiQuery)({ name: "endDate", required: false }),
    (0, swagger_1.ApiQuery)({ name: "userId", required: false }),
    (0, swagger_1.ApiQuery)({ name: "robotId", required: false }),
    (0, swagger_1.ApiQuery)({ name: "action", required: false }),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __param(2, (0, common_1.Query)('userId')),
    __param(3, (0, common_1.Query)('robotId')),
    __param(4, (0, common_1.Query)('action')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String]),
    __metadata("design:returntype", void 0)
], HistoryController.prototype, "exportData", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get history record by ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], HistoryController.prototype, "findOne", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(roles_decorator_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete history record (Admin only)' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], HistoryController.prototype, "remove", null);
exports.HistoryController = HistoryController = __decorate([
    (0, swagger_1.ApiTags)("History"),
    (0, common_1.Controller)("history"),
    (0, common_1.UseGuards)(firebase_auth_guard_1.FirebaseAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [history_service_1.HistoryService])
], HistoryController);
//# sourceMappingURL=history.controller.js.map