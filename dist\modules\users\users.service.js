"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const firebase_service_1 = require("../../config/firebase/firebase.service");
let UsersService = class UsersService {
    constructor(firebaseService) {
        this.firebaseService = firebaseService;
        this.collection = "users";
    }
    async create(createUserDto) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(createUserDto.userId);
        await docRef.set(createUserDto);
        return createUserDto;
    }
    async findAll() {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).get();
        return snapshot.docs.map((doc) => ({ id: doc.id, ...doc.data() }));
    }
    async findById(id) {
        const firestore = this.firebaseService.getFirestore();
        const doc = await firestore.collection(this.collection).doc(id).get();
        if (!doc.exists) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        return { id: doc.id, ...doc.data() };
    }
    async findByEmail(email) {
        const firestore = this.firebaseService.getFirestore();
        const snapshot = await firestore.collection(this.collection).where('email', '==', email).get();
        if (snapshot.empty) {
            return null;
        }
        const doc = snapshot.docs[0];
        return { id: doc.id, ...doc.data() };
    }
    async update(id, updateUserDto) {
        try {
            const firestore = this.firebaseService.getFirestore();
            const docRef = firestore.collection(this.collection).doc(id);
            const doc = await docRef.get();
            if (!doc.exists) {
                throw new common_1.NotFoundException(`User with ID ${id} not found`);
            }
            await docRef.update({
                ...updateUserDto,
                updatedAt: new Date(),
            });
            return this.findById(id);
        }
        catch (error) {
            console.error('Error updating user:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new Error(`Failed to update user: ${error.message}`);
        }
    }
    async updateLastLogin(userId) {
        const firestore = this.firebaseService.getFirestore();
        const docRef = firestore.collection(this.collection).doc(userId);
        await docRef.update({
            lastLogin: new Date(),
        });
    }
    async remove(id) {
        try {
            const firestore = this.firebaseService.getFirestore();
            const doc = await firestore.collection(this.collection).doc(id).get();
            if (!doc.exists) {
                throw new common_1.NotFoundException(`User with ID ${id} not found`);
            }
            const userData = doc.data();
            const firebaseAuthId = userData?.userId || id;
            await firestore.collection(this.collection).doc(id).delete();
            try {
                await this.firebaseService.getAuth().deleteUser(firebaseAuthId);
            }
            catch (authError) {
                console.warn(`Could not delete user from Firebase Auth: ${authError.message}`);
            }
            return { message: "User deleted successfully" };
        }
        catch (error) {
            console.error('Error deleting user:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new Error(`Failed to delete user: ${error.message}`);
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [firebase_service_1.FirebaseService])
], UsersService);
//# sourceMappingURL=users.service.js.map