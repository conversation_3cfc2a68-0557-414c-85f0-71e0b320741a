import type { FloorsService } from "./floors.service";
import type { CreateFloorDto } from "./dto/create-floor.dto";
import type { UpdateFloorDto } from "./dto/update-floor.dto";
export declare class FloorsController {
    private readonly floorsService;
    constructor(floorsService: FloorsService);
    create(createFloorDto: CreateFloorDto): Promise<{
        floorId: string;
        isActive: boolean;
        createdAt: Date;
        floorNumber: number;
        buildingId: string;
        floorName: string;
        totalRooms: number;
        mapData?: string;
    }>;
    findAll(buildingId?: string): Promise<{
        id: string;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateFloorDto: UpdateFloorDto): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
