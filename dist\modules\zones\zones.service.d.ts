import type { FirebaseService } from "../../config/firebase/firebase.service";
import type { CreateZoneDto } from "./dto/create-zone.dto";
import type { UpdateZoneDto } from "./dto/update-zone.dto";
export declare class ZonesService {
    private firebaseService;
    private readonly collection;
    constructor(firebaseService: FirebaseService);
    create(createZoneDto: CreateZoneDto): Promise<{
        zoneId: string;
        createdAt: Date;
        zoneName: string;
        roomId: string;
        coordinates: string;
        zoneType: string;
        priority: string;
        disinfectionRequired?: boolean;
        accessPoints?: string;
        lastDisinfected?: string;
        disinfectionFrequency?: number;
    }>;
    findAll(): Promise<{
        id: string;
    }[]>;
    findByRoom(roomId: string): Promise<{
        id: string;
    }[]>;
    findById(id: string): Promise<{
        id: string;
    }>;
    update(id: string, updateZoneDto: UpdateZoneDto): Promise<{
        id: string;
    }>;
    markAsDisinfected(id: string): Promise<{
        id: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
